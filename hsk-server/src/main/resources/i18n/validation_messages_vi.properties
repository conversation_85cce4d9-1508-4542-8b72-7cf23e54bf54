# Thông báo xác thực chung
javax.validation.constraints.NotBlank.message=không thể để trống
javax.validation.constraints.NotNull.message=không thể là null
javax.validation.constraints.NotEmpty.message=không thể để trống
javax.validation.constraints.Size.message=kích thước phải nằm trong khoảng {min} và {max}
javax.validation.constraints.Min.message=phải lớn hơn hoặc bằng {value}
javax.validation.constraints.Max.message=phải nhỏ hơn hoặc bằng {value}
javax.validation.constraints.Email.message=không phải là địa chỉ email hợp lệ
javax.validation.constraints.Pattern.message=định dạng không chính xác
# Thông báo xác thực tùy chỉnh
validation.id-card.invalid=định dạng số thẻ ID không chính xác
validation.password.weak=độ mạnh mật khẩu không đủ
validation.username.invalid=định dạng tên người dùng không chính xác
# ============================登录注册验证消息================================
# 国家区号错误
validation.invalid.country.code=Mã vùng quốc gia không đúng
#手机号格式错误
validation.mobile.invalid=Định dạng số điện thoại không đúng
validation.mobile.invalid_2=Vui lòng nhập đúng số điện thoại
#验证码必须是6位数字
validation.verification.code.invalid=Mã xác minh phải có 6 chữ số
#密码格式不正确
validation.password.invalid=Mật khẩu phải từ 8-16 ký tự, bao gồm chữ cái và số

#=============================用户反馈===================================
#反馈内容为空
validation.feedback.content.empty=Vui lòng điền nội dung phản hồi trước