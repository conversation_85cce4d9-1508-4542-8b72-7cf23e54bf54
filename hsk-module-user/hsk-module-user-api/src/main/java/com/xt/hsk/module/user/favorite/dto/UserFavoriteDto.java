package com.xt.hsk.module.user.favorite.dto;

import com.xt.hsk.module.user.enums.FavoriteSourceEnum;
import com.xt.hsk.module.user.enums.FavoriteTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户题目收藏分页列表响应
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
public class UserFavoriteDto {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 收藏类型：1-字词 2-真题题目 3-小游戏题目
     *
     * @see FavoriteTypeEnum
     */
    private Integer favoriteType;
    /**
     * 收藏来源：1-互动课作业 2-真题练习 3-专项练习
     *
     * @see FavoriteSourceEnum
     */
    private Integer favoriteSource;
    /**
     * 目标ID（字词ID | 题目ID | 小游戏题目ID）
     */
    private Long targetId;
    /**
     * 目标明细id 存在时说明收藏的是一部分内容
     */
    private Long targetDetailId;
    /**
     * 游戏 ID 收藏游戏题目时需要用到，用于查询题目难度
     */
    private Long gameId;
    /**
     * 是否错题：1-是 0-否
     */
    private Boolean isWrongQuestion;
    /**
     * 收藏时间
     */
    private LocalDateTime favoriteTime;
    /**
     * 取消收藏时间
     */
    private LocalDateTime cancelTime;

}