package com.xt.hsk.module.user.controller.app.user.vo;

import com.xt.hsk.framework.common.constants.RegexpConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 更换手机号 Request VO
 *
 * <AUTHOR>
 * @since 2025/07/21
 */
@Schema(description = "更换手机号 Request VO")
@Data
public class ChangeMobileReqVO {

    /**
     * 新手机号
     */
    @Pattern(regexp = RegexpConstant.MOBILE, message = "{validation.mobile.invalid_2}")
    private String newMobile;

    /**
     * 新手机区号
     */
    @Pattern(regexp = RegexpConstant.COUNTRY_CODE, message = "{validation.invalid.country.code}")
    private String newCountryCode;

    /**
     * 新手机号验证码
     */
    @Pattern(regexp = RegexpConstant.MOBILE_CODE, message = "{validation.verification.code.invalid}")
    private String newCode;

}
