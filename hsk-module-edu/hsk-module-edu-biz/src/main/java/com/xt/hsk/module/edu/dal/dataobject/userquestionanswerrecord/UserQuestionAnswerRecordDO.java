package com.xt.hsk.module.edu.dal.dataobject.userquestionanswerrecord;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.BaseDO;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 用户题目作答记录 DO
 *
 * <AUTHOR>
 */
@TableName("edu_user_question_answer_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserQuestionAnswerRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 题目ID
     */
    private Long questionId;
    /**
     * HSK等级
     */
    private Integer hskLevel;
    /**
     * 教材ID
     */
    private Long textbookId;
    /**
     * 章节ID
     */
    private Long chapterId;
    /**
     * 单元ID
     */
    private Long unitId;
    /**
     * 科目 1听力 2阅读 4写作
     */
    private Integer subject;
    /**
     * 题型ID
     */
    private Long questionTypeId;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 题目版本库id
     */
    private Long questionVersionId;
    /**
     * 练习模式：1-单独练习 2-全真模考 3-30分钟模考 4-15分钟模考 5 互动课 6 收藏夹 7 等级考试
     * @see com.xt.hsk.framework.common.enums.PracticeModeEnum
     */
    private Integer practiceMode;
    /**
     * 考试/练习ID（关联到具体的考试或练习）
     */
    private Long practiceId;
    /**
     * 作答耗时（秒）
     */
    private Integer answerTime;
    /**
     * 作答日期（便于按日统计）
     */
    private LocalDate answerDate;
    /**
     * 开始作答时间
     */
    private LocalDateTime startTime;
    /**
     * 结束作答时间
     */
    private LocalDateTime endTime;
    /**
     * 记录状态 1 进行中 2 已提交 3 ai批改未完成  4 ai批改完成 5 ai批改失败
     */
    private Integer recordStatus;
    /**
     * 题目总数量
     */
    private Integer questionNum;
    /**
     * 已正确数量
     */
    private Integer correctNum;
    /**
     * 题目总分
     */
    private Integer totalScore;
    /**
     * 用户得分
     */
    private Integer userScore;
    /**
     * 单元部分
     */
    private Integer unitSort;

}