package com.xt.hsk.module.thirdparty.api;

/**
 * 讯飞接口api
 */
public interface XunFeiApi {

    /**
     * 上传文件到讯飞
     *
     * @param url         文件url
     * @param callbackUrl 回调地址
     * @return
     */
    String upload(String url, String callbackUrl);

    /**
     * 获取结果
     *
     * @param orderId
     * @return
     */
    String getResult(String orderId);

    /**
     * 机器翻译
     *
     * @param text 待翻译文本
     * @param from 源语言 (cn:中文, en:英文, vi:越南语等)
     * @param to   目标语言 (cn:中文, en:英文, vi:越南语等)
     * @return 翻译结果
     */
    String translate(String text, String from, String to);
}
