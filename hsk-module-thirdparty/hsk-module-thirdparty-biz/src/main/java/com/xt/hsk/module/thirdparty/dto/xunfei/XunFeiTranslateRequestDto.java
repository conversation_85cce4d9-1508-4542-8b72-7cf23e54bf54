package com.xt.hsk.module.thirdparty.dto.xunfei;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 讯飞翻译请求DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XunFeiTranslateRequestDto {

    /**
     * 待翻译文本
     */
    private String text;

    /**
     * 源语言
     * cn:中文, en:英文, vi:越南语, ja:日语, ko:韩语, fr:法语, de:德语, es:西班牙语, ru:俄语等
     */
    private String from;

    /**
     * 目标语言
     * cn:中文, en:英文, vi:越南语, ja:日语, ko:韩语, fr:法语, de:德语, es:西班牙语, ru:俄语等
     */
    private String to;
}
