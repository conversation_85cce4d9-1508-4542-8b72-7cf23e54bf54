package com.xt.hsk.module.thirdparty.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xt.hsk.module.thirdparty.api.XunFeiApi;
import com.xt.hsk.module.thirdparty.config.xunfei.XunFeiConfig;
import com.xt.hsk.module.thirdparty.dto.xunfei.XunFeiTranslateRequestDto;
import com.xt.hsk.module.thirdparty.dto.xunfei.XunFeiTranslateResponseDto;
import com.xt.hsk.module.thirdparty.utils.FileUtils;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FrameGrabber;
import org.springframework.stereotype.Service;

/**
 * 讯飞语音接口
 */
@Slf4j
@Service
public class XunFeiServiceImpl implements XunFeiApi {
    @Resource
    private XunFeiConfig xunFeiConfig;
    private static final String SERVICE_URL = "https://api.iflyrec.com";
    private static String SERVICE_URL_UPLOAD = SERVICE_URL + "/v2/upload";
    private static String SERVICE_URL_GET_RESULT = SERVICE_URL + "/v2/getResult";

    // 讯飞翻译API地址
    private static final String TRANSLATE_URL = "https://itrans.xfyun.cn/v2/its";

    @Override
    public String upload(String fileUrl, String callbackUrl) {
        FFmpegFrameGrabber grabber = null;
        InputStream inputStream = null;
        File file = null;
        try {
            String encode = FileUtils.setUrL(fileUrl);
            URL url = new URL(encode);
            inputStream = url.openStream();
            file = File.createTempFile("temp", ".mp3");
            try (FileOutputStream fos = new FileOutputStream(file)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }
            String fileName = file.getName();
            long fileSize = file.length();
            Map<String, Object> map = new TreeMap<String, Object>();
            map.put("fileName", fileName);
            map.put("fileSize", fileSize);
            grabber = FFmpegFrameGrabber.createDefault(encode);
            grabber.start();
            map.put("duration", grabber.getLengthInTime() / (1000));//真实的音频时长
            map.put("roleType", 1);
            map.put("callbackUrl", xunFeiConfig.getCallbackUrlBase() + callbackUrl);
            map.put("transMode", 1);
            map.put("dateTime", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(new Date()));
            map.put("accessKeyId", xunFeiConfig.getAccessKey());
            map.put("signatureRandom", UUID.randomUUID().toString());
            map.put("language", "cn");


            String string = requestPost(SERVICE_URL_UPLOAD, map, Files.readAllBytes(file.toPath()));
            log.info("上传结果:{}", string);


        } catch (Exception e) {
            log.warn("系统异常 讯飞题目音转写:", e);
        } finally {
            deleteFile(file, inputStream, grabber);
        }
        return "";
    }

    @Override
    public String getResult(String orderId) {
        // ☆☆☆使用TreeMap对内容根据Key进行自然排序
        Map<String, Object> map = new TreeMap<String, Object>();
        map.put("dateTime", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(new Date()));
        map.put("signatureRandom", UUID.randomUUID().toString());
        map.put("accessKeyId", xunFeiConfig.getAccessKey());
        map.put("orderId", orderId);//订单ID
        String result = null;
        try {
            result = requestGet(SERVICE_URL_GET_RESULT, map);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        String res = "";
        if (result != null) {
            // 解析中文字符串
            res = parsingChinese(result);
        }
        return res;
    }

    private String parsingChinese(String result) {
        try {
            // 1. 解析最外层JSON
            JSONObject root = JSONObject.parseObject(result);
            JSONObject content = root.getJSONObject("content");
//            JSONObject orderInfo = content.getJSONObject("orderResult");

            // 2. 获取orderResult字符串并解析为JSON对象
            String orderResultStr = content.getString("orderResult");
            JSONObject orderResultJson = JSONObject.parseObject(orderResultStr);

            // 3. 提取lattice数组的第一个元素
            JSONArray lattice = orderResultJson.getJSONArray("lattice");
            JSONObject firstLattice = lattice.getJSONObject(0);

            // 4. 解析json_1best字符串
            String json1bestStr = firstLattice.getString("json_1best");
            JSONObject json1best = JSONObject.parseObject(json1bestStr);
            JSONObject st = json1best.getJSONObject("st");

            // 5. 遍历rt[0].ws数组拼接中文文本
            JSONArray rt = st.getJSONArray("rt");
            JSONObject firstRt = rt.getJSONObject(0);
            JSONArray ws = firstRt.getJSONArray("ws");

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < ws.size(); i++) {
                JSONObject wordSeg = ws.getJSONObject(i);
                JSONArray cw = wordSeg.getJSONArray("cw");
                JSONObject word = cw.getJSONObject(0);
                String w = word.getString("w");
                // 跳过空字符串
                if (!w.isEmpty()) {
                    sb.append(w);
                }
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("解析讯飞结果失败:{}", e.getMessage(), e);
        }
        return null;
    }

    private String requestPost(String url, Map<String, Object> map,
                               byte[] uploadContent) throws Exception {
        StringBuilder formUrlEncodedString = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null) {
                formUrlEncodedString.append(entry.getKey()).append("=")
                        .append(URLEncoder.encode(String.valueOf(entry.getValue()),
                                StandardCharsets.UTF_8))
                        .append("&");
            }
        }
        if (formUrlEncodedString.length() > 1) {
            formUrlEncodedString
                    .setLength(formUrlEncodedString.length() - 1);
        }
        String signature = getXunFeiSign(formUrlEncodedString.toString());
        String uploadUrl = url + "?" + formUrlEncodedString.toString();
        HttpRequest request = HttpRequest.post(uploadUrl);
        request.header("signature", signature);
        request.header("application/json", "UTF-8");
        request.body(uploadContent);
        HttpResponse execute = request.execute();
        int statusCode = execute.getStatus();
        if (statusCode != 200) {
            String message = "call servie failed: "
                    + execute.getStatus();
            log.error(message);
        }
        String body = execute.body();
        return body;
    }

    private String requestGet(String url, Map<String, Object> map) throws Exception {
        StringBuilder formUrlEncodedString = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null) {
                formUrlEncodedString.append(entry.getKey()).append("=")
                        .append(URLEncoder.encode(String.valueOf(entry.getValue()),
                                StandardCharsets.UTF_8))
                        .append("&");
            }
        }
        if (formUrlEncodedString.length() > 1) {
            formUrlEncodedString
                    .setLength(formUrlEncodedString.length() - 1);
        }
        String signature = getXunFeiSign(formUrlEncodedString.toString());
        HttpRequest httpRequest = HttpRequest.get(url + "?" + formUrlEncodedString.toString());
        httpRequest.header("signature", signature);
        HttpResponse execute = httpRequest.execute();
        return execute.body();
    }

    private void deleteFile(File file, InputStream inputStream, FFmpegFrameGrabber grabber) {
        if (file != null) {
            file.delete();
        }

        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
            }
        }

        if (grabber != null) {
            try {
                grabber.stop();
            } catch (FrameGrabber.Exception e) {
            }
        }
    }


    private String getXunFeiSign(String formUrlEncodedString) throws Exception {

        byte[] signBytes = hmacSHA1Signature(xunFeiConfig.getAccessSecret(), formUrlEncodedString);

        return Base64.getEncoder().encodeToString(signBytes);

    }


    public static byte[] hmacSHA1Signature(String secret, String baseString)
            throws Exception {
        if (StrUtil.isEmpty(secret)) {
            throw new IOException("secret can not be empty");
        }
        if (StrUtil.isEmpty(baseString)) {
            return null;
        }
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec keySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8),
                "UTF-8");
        mac.init(keySpec);
        return mac.doFinal(baseString.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public String translate(String text, String from, String to) {
        if (StrUtil.isEmpty(text)) {
            log.warn("翻译文本为空");
            return "";
        }

        try {
            return doTranslateHttp(text, from, to);
        } catch (Exception e) {
            log.error("讯飞翻译异常: text={}, from={}, to={}", text, from, to, e);
            return "";
        }
    }

    /**
     * 执行HTTP翻译请求
     */
    private String doTranslateHttp(String text, String from, String to) throws Exception {
        // 构建请求体
        JSONObject requestBody = new JSONObject();
        JSONObject common = new JSONObject();
        common.put("app_id", xunFeiConfig.getAppId());

        JSONObject business = new JSONObject();
        business.put("from", from);
        business.put("to", to);

        JSONObject data = new JSONObject();
        // 对文本进行Base64编码
        String encodedText = Base64.getEncoder().encodeToString(text.getBytes(StandardCharsets.UTF_8));
        data.put("text", encodedText);

        requestBody.put("common", common);
        requestBody.put("business", business);
        requestBody.put("data", data);

        String requestBodyStr = requestBody.toJSONString();

        // 发送POST请求
        String response = requestTranslatePost(TRANSLATE_URL, requestBodyStr);

        // 解析响应
        return parseTranslateHttpResponse(response);
    }



    /**
     * 发送翻译POST请求
     */
    private String requestTranslatePost(String url, String requestBody) throws Exception {
        // 生成鉴权头部
        String host = "itrans.xfyun.cn";
        String date = generateRFC1123Date();
        String digest = generateDigest(requestBody);
        String authorization = generateAuthorization(host, date, digest, requestBody);

        // 发送POST请求
        HttpRequest request = HttpRequest.post(url);
        request.header("Content-Type", "application/json");
        request.header("Accept", "application/json,version=1.0");
        request.header("Host", host);
        request.header("Date", date);
        request.header("Digest", digest);
        request.header("Authorization", authorization);
        request.body(requestBody);

        HttpResponse response = request.execute();

        if (response.getStatus() != 200) {
            log.error("翻译请求失败: status={}, body={}, headers={}",
                response.getStatus(), response.body(), response.headers());
            throw new RuntimeException("翻译请求失败: " + response.getStatus() + ", " + response.body());
        }

        return response.body();
    }

    /**
     * 生成RFC1123格式的日期
     */
    private String generateRFC1123Date() {
        SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", java.util.Locale.ENGLISH);
        sdf.setTimeZone(java.util.TimeZone.getTimeZone("GMT"));
        return sdf.format(new Date());
    }

    /**
     * 生成Digest头部
     */
    private String generateDigest(String requestBody) throws Exception {
        MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
        byte[] hash = sha256.digest(requestBody.getBytes(StandardCharsets.UTF_8));
        return "SHA-256=" + Base64.getEncoder().encodeToString(hash);
    }

    /**
     * 生成Authorization头部
     */
    private String generateAuthorization(String host, String date, String digest, String requestBody) throws Exception {
        // 构建签名字符串
        String signatureOrigin = "host: " + host + "\n" +
                                "date: " + date + "\n" +
                                "POST /v2/its HTTP/1.1" + "\n" +
                                "digest: " + digest;

        // 使用HMAC-SHA256签名
        byte[] signatureBytes = hmacSHA256Signature(xunFeiConfig.getAccessSecret(), signatureOrigin);
        String signature = Base64.getEncoder().encodeToString(signatureBytes);

        // 构建Authorization头部
        return String.format("api_key=\"%s\", algorithm=\"hmac-sha256\", headers=\"host date request-line digest\", signature=\"%s\"",
                xunFeiConfig.getAccessKey(), signature);
    }

    /**
     * HMAC-SHA256签名
     */
    private byte[] hmacSHA256Signature(String secret, String data) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec keySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(keySpec);
        return mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
    }


    /**
     * 解析HTTP翻译响应
     */
    private String parseTranslateHttpResponse(String response) {
        try {
            log.info("翻译响应: {}", response);
            JSONObject jsonResponse = JSONObject.parseObject(response);

            // 检查返回码
            Integer code = jsonResponse.getInteger("code");
            if (code == null || code != 0) {
                String message = jsonResponse.getString("message");
                log.error("翻译失败: code={}, message={}", code, message);
                return "";
            }

            // 解析翻译结果
            if (jsonResponse.containsKey("data")) {
                JSONObject data = jsonResponse.getJSONObject("data");
                if (data != null && data.containsKey("result")) {
                    JSONObject result = data.getJSONObject("result");
                    if (result != null && result.containsKey("trans_result")) {
                        JSONObject transResult = result.getJSONObject("trans_result");
                        if (transResult != null) {
                            return transResult.getString("dst");
                        }
                    }
                }
            }

            log.warn("未找到翻译结果: {}", response);
            return "";
        } catch (Exception e) {
            log.error("解析翻译响应失败: {}", response, e);
            return "";
        }
    }
}
