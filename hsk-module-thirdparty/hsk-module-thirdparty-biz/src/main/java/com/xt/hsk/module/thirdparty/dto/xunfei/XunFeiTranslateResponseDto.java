package com.xt.hsk.module.thirdparty.dto.xunfei;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 讯飞翻译响应DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XunFeiTranslateResponseDto {

    /**
     * 响应码
     * 0: 成功
     * 其他: 失败
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 翻译结果
     */
    private String result;

    /**
     * 请求ID
     */
    private String sid;

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 0;
    }
}
