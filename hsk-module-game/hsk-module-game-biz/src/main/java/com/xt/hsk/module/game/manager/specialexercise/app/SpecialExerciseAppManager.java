package com.xt.hsk.module.game.manager.specialexercise.app;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionAppRespVO;
import com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO;
import com.xt.hsk.module.game.controller.app.specialexercise.vo.*;
import com.xt.hsk.module.game.convert.exercisequestion.ExerciseQuestionConvert;
import com.xt.hsk.module.game.convert.specialexercise.SpecialExerciseConvert;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionDO;
import com.xt.hsk.module.game.dal.dataobject.exercisequestion.ExerciseQuestionVersionDO;
import com.xt.hsk.module.game.dal.dataobject.record.GameRecordDetailsDO;
import com.xt.hsk.module.game.dal.dataobject.record.GameRecordSummaryDO;
import com.xt.hsk.module.game.dal.dataobject.specialexercise.SpecialExerciseDO;
import com.xt.hsk.module.game.enums.record.GameRecordCorrectTypeEnum;
import com.xt.hsk.module.game.enums.record.GameRecordPracticeStatusEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseAnswerModeEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseRecordSourceEnum;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import com.xt.hsk.module.game.manager.correction.app.GameCorrectionAppManager;
import com.xt.hsk.module.game.producer.game.GameProducer;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionService;
import com.xt.hsk.module.game.service.exercisequestion.ExerciseQuestionVersionService;
import com.xt.hsk.module.game.service.record.GameRecordDetailsService;
import com.xt.hsk.module.game.service.record.GameRecordSummaryService;
import com.xt.hsk.module.game.service.specialexercise.SpecialExerciseService;
import com.xt.hsk.module.user.enums.FavoriteTypeEnum;
import com.xt.hsk.module.user.favorite.UserFavoriteApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.game.enums.ErrorCodeConstants.*;

/**
 * 游戏-专项练习-练习组 app Manager
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Slf4j
@Component
public class SpecialExerciseAppManager {

    private static final BigDecimal COMPLETE_PROGRESS = new BigDecimal("100");

    @Resource
    private SpecialExerciseService specialExerciseService;

    @Resource
    private ExerciseQuestionVersionService exerciseQuestionVersionService;

    @Resource
    private GameRecordSummaryService gameRecordSummaryService;

    @Resource
    private GameRecordDetailsService gameRecordDetailsService;

    @Resource
    private ExerciseQuestionService exerciseQuestionService;

    @Resource
    private GameProducer gameProducer;

    @Resource
    private GameCorrectionAppManager gameCorrectionAppManager;

    @Resource
    private UserFavoriteApi userFavoriteApi;

    public PageResult<SpecialExerciseAppPageRespVO> getSpecialExercisePage(SpecialExerciseAppPageReqVO reqVO) {
        if (SpecialExerciseTypeEnum.getByCode(reqVO.getType()) == null) {
            return PageResult.empty();
        }

        if (StpUtil.isLogin()) {
            reqVO.setUserId(StpUtil.getLoginIdAsLong());
        }

        // 设置来源
        reqVO.setSource(SpecialExerciseRecordSourceEnum.SPECIAL_EXERCISE.getCode());

        PageResult<SpecialExerciseRecordRespVO> page = specialExerciseService.getAppSpecialExercisePage(reqVO);

        List<SpecialExerciseAppPageRespVO> voList = SpecialExerciseConvert.INSTANCE.toAppPageVOList(page.getList());

        // 设置练习状态
        setPracticeStatus(voList);

        return new PageResult<>(voList, page.getTotal());
    }

    /**
     * 设置练习状态
     *
     * @param voList VO列表
     */
    private void setPracticeStatus(List<SpecialExerciseAppPageRespVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        List<Long> specialExerciseIdList = voList.stream()
                .map(SpecialExerciseAppPageRespVO::getSpecialExerciseId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        // 批量查询专项练习题目数量并按专项练习ID分组
        Map<Long, Integer> sizeMap = getQuestionsSizeBySpecialIdList(specialExerciseIdList);

        voList.forEach(vo -> {
            if (vo.getRecordSummaryId() == null) {
                vo.setPracticeStatus(GameRecordPracticeStatusEnum.NOT_STARTED.getCode());
                vo.setProgress(0);
                vo.setWrongQuestions(0);
                vo.setCorrectQuestions(0);
                vo.setTotalQuestions(sizeMap.getOrDefault(vo.getSpecialExerciseId(), 0));
            }
        });
    }

    /**
     * 设置作答信息
     */
    private void setAnswerInfo(List<SpecialExerciseAppPageRespVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        // 获取所有专项练习ID
        List<Long> specialExerciseIdList = voList.stream()
                .map(SpecialExerciseAppPageRespVO::getSpecialExerciseId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        // 批量查询题目信息并按专项练习ID分组
        Map<Long, List<ExerciseQuestionDO>> questionMap = getQuestionsGroupedBySpecialIdList(specialExerciseIdList);

        // 获取记录汇总ID列表
        List<Long> recordSummaryIdList = voList.stream()
                .map(SpecialExerciseAppPageRespVO::getRecordSummaryId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        // 获取答题记录并按记录汇总ID分组
        Map<Long, List<GameRecordDetailsDO>> recordDetailsMap = getRecordDetailsGroupedBySummary(
                recordSummaryIdList, questionMap);

        // 为每个VO设置答题信息
        voList.forEach(vo -> setVoAnswerInfo(vo, questionMap, recordDetailsMap));
    }

    /**
     * 获取题目信息并按专项练习ID分组
     */
    private Map<Long, List<ExerciseQuestionDO>> getQuestionsGroupedBySpecialIdList(List<Long> specialExerciseIdList) {
        if (CollUtil.isEmpty(specialExerciseIdList)) {
            return Collections.emptyMap();
        }

        return exerciseQuestionService.lambdaQuery()
                .in(ExerciseQuestionDO::getSpecialExerciseId, specialExerciseIdList)
                .list()
                .stream()
                .collect(Collectors.groupingBy(ExerciseQuestionDO::getSpecialExerciseId));
    }

    /**
     * 获取题目信息并按专项练习ID分组
     */
    private Map<Long, Integer> getQuestionsSizeBySpecialIdList(List<Long> specialExerciseIdList) {
        if (CollUtil.isEmpty(specialExerciseIdList)) {
            return Collections.emptyMap();
        }

        return exerciseQuestionService.lambdaQuery()
                .in(ExerciseQuestionDO::getSpecialExerciseId, specialExerciseIdList)
                .list()
                .stream()
                .collect(Collectors.groupingBy(
                        ExerciseQuestionDO::getSpecialExerciseId,
                        Collectors.collectingAndThen(Collectors.counting(), Long::intValue)
                ));
    }

    /**
     * 获取答题记录并按记录汇总ID分组
     */
    private Map<Long, List<GameRecordDetailsDO>> getRecordDetailsGroupedBySummary(
            List<Long> recordSummaryIdList, Map<Long, List<ExerciseQuestionDO>> questionMap) {

        if (CollUtil.isEmpty(recordSummaryIdList)) {
            return Collections.emptyMap();
        }

        List<Long> exerciseQuestionIdList = questionMap.values().stream()
                .flatMap(List::stream)
                .map(ExerciseQuestionDO::getId)
                .toList();

        if (CollUtil.isEmpty(exerciseQuestionIdList)) {
            return Collections.emptyMap();
        }

        return gameRecordDetailsService.lambdaQuery()
                .in(GameRecordDetailsDO::getRecordSummaryId, recordSummaryIdList)
                .in(GameRecordDetailsDO::getExerciseQuestionId, exerciseQuestionIdList)
                .eq(GameRecordDetailsDO::getAnswerSequence, 1)
                .list()
                .stream()
                .collect(Collectors.groupingBy(GameRecordDetailsDO::getRecordSummaryId));
    }

    /**
     * 为单个VO设置答题信息
     */
    private void setVoAnswerInfo(SpecialExerciseAppPageRespVO vo,
                                 Map<Long, List<ExerciseQuestionDO>> questionMap,
                                 Map<Long, List<GameRecordDetailsDO>> recordDetailsMap) {

        // 如果没有记录汇总ID，设置为未开始状态
        if (vo.getRecordSummaryId() == null) {
            // 获取该专项练习的题目数量
            List<ExerciseQuestionDO> questionList = questionMap.getOrDefault(vo.getSpecialExerciseId(), Collections.emptyList());
            vo.setTotalQuestions(questionList.size());
            setDefaultAnswerInfo(vo);
            return;
        }

        // 获取该记录的答题详情
        List<GameRecordDetailsDO> recordDetailsList = recordDetailsMap.getOrDefault(vo.getRecordSummaryId(), Collections.emptyList());

        if (CollUtil.isEmpty(recordDetailsList)) {
            // 获取该专项练习的题目数量
            List<ExerciseQuestionDO> questionList = questionMap.getOrDefault(vo.getSpecialExerciseId(), Collections.emptyList());
            vo.setTotalQuestions(questionList.size());
            vo.setCorrectQuestions(0);
            vo.setWrongQuestions(0);
            vo.setProgress(0);
            return;
        }

        // 计算正确和错误题目数量
        long correctCount = recordDetailsList.stream()
                .filter(detail -> GameRecordCorrectTypeEnum.CORRECT.getCode().equals(detail.getIsCorrect()))
                .count();

        long wrongCount = recordDetailsList.stream()
                .filter(detail -> GameRecordCorrectTypeEnum.ERROR.getCode().equals(detail.getIsCorrect()))
                .count();

        vo.setCorrectQuestions(Math.toIntExact(correctCount));
        vo.setWrongQuestions(Math.toIntExact(wrongCount));
        vo.setTotalQuestions(recordDetailsList.size());

        // 计算进度
        int progress = calculateProgress(correctCount + wrongCount, recordDetailsList.size());
        vo.setProgress(progress);
    }

    /**
     * 设置默认答题信息（未开始状态）
     */
    private void setDefaultAnswerInfo(SpecialExerciseAppPageRespVO vo) {
        vo.setCorrectQuestions(0);
        vo.setWrongQuestions(0);
        vo.setProgress(0);
        vo.setPracticeStatus(GameRecordPracticeStatusEnum.NOT_STARTED.getCode());
    }

    /**
     * 计算进度百分比
     */
    private int calculateProgress(long answeredQuestions, int totalQuestions) {
        if (totalQuestions <= 0) {
            return 0;
        }

        return BigDecimal.valueOf(answeredQuestions)
                .divide(BigDecimal.valueOf(totalQuestions), 4, RoundingMode.HALF_UP)
                .multiply(COMPLETE_PROGRESS)
                .intValue();
    }

    /**
     * 获得专项练习id获取专项练习题目
     */
    public SpecialExerciseAppRespVO getSpecialExercise(SpecialExerciseAppReqVO reqVO) {
        Long specialExerciseId = reqVO.getSpecialExerciseId();
        Integer type = reqVO.getType();
        Integer answerMode = reqVO.getAnswerType();
        long userId = StpUtil.getLoginIdAsLong();

        // 验证专项练习是否存在且可见
        SpecialExerciseDO specialExercise = specialExerciseService.getById(specialExerciseId);
        if (specialExercise == null || !IsShowEnum.SHOW.getCode().equals(specialExercise.getIsShow())) {
            throw exception(SPECIAL_EXERCISE_NOT_EXISTS);
        }

        // 获取已练习过的题目版本ID列表
        List<Long> practicedQuestionVersionIds = getPracticedQuestionVersionIds(specialExerciseId, reqVO.getRecordSummaryId(), type, answerMode);

        // 获取题目列表
        List<ExerciseQuestionRespVO> respVOList = exerciseQuestionVersionService.getExerciseQuestion(specialExerciseId, type, specialExercise.getRelatedVersion());

        // 构建基础响应对象
        SpecialExerciseAppRespVO vo = SpecialExerciseConvert.INSTANCE.doToAppRespVo(specialExercise);
        if (CollUtil.isEmpty(respVOList)) {
            return vo;
        }
        
        // 转换为APP响应对象列表
        List<ExerciseQuestionAppRespVO> appRespVoList = ExerciseQuestionConvert.INSTANCE.respVoListToAppRespVoList(respVOList);
        
        // 根据不同类型处理数据
        if (SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(type)) {
            return handleWordMatchingExercise(vo, appRespVoList, practicedQuestionVersionIds, respVOList);
        } else {
            // 设置收藏状态
            setFavoriteStatus(appRespVoList, userId, type);
            return handleOtherExerciseTypes(vo, appRespVoList, practicedQuestionVersionIds, respVOList, type);
        }
    }

    /**
     * 获取已练习过的题目版本ID列表
     */
    private List<Long> getPracticedQuestionVersionIds(Long specialExerciseId, Long recordSummaryId, Integer type, Integer answerMode) {
        if (!SpecialExerciseAnswerModeEnum.CONTINUE.getCode().equals(answerMode)) {
            return new ArrayList<>();
        }

        // 验证记录是否存在且未完成
        GameRecordSummaryDO recordSummary = gameRecordSummaryService.getById(recordSummaryId);
        if (recordSummary == null) {
            throw exception(GAME_RECORD_NOT_EXISTS);
        }
        if (GameRecordPracticeStatusEnum.COMPLETED.getCode().equals(recordSummary.getPracticeStatus())) {
            throw exception(GAME_RECORD_COMPLETED);
        }

        return gameRecordDetailsService.lambdaQuery()
                .eq(GameRecordDetailsDO::getSpecialExerciseId, specialExerciseId)
                .eq(GameRecordDetailsDO::getRecordSummaryId, recordSummaryId)
                .eq(GameRecordDetailsDO::getType, type)
                .list()
                .stream()
                .map(GameRecordDetailsDO::getExerciseQuestionVersionId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
    }

    /**
     * 处理单词连连看类型的练习
     */
    private SpecialExerciseAppRespVO handleWordMatchingExercise(SpecialExerciseAppRespVO vo, 
                                                           List<ExerciseQuestionAppRespVO> appRespVoList,
                                                           List<Long> practicedQuestionVersionIds,
                                                           List<ExerciseQuestionRespVO> respVOList) {
        // 计算已完成的轮次
        int completedRound = (int) respVOList.stream()
                .filter(question -> practicedQuestionVersionIds.contains(question.getExerciseQuestionVersionId()))
                .map(ExerciseQuestionRespVO::getRoundNumber)
                .filter(Objects::nonNull)
                .distinct()
                .count();
        
        // 按轮次分组
        Map<Integer, List<ExerciseQuestionAppRespVO>> roundNumberMap = appRespVoList.stream()
                .sorted(Comparator.comparing(ExerciseQuestionAppRespVO::getRoundNumber))
                .collect(Collectors.groupingBy(ExerciseQuestionAppRespVO::getRoundNumber));
        
        int totalRound = roundNumberMap.size();
        
        // 构建轮次列表
        List<ExerciseRoundAppRespVO> roundList = roundNumberMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ))
                .values().stream()
                .map(ExerciseRoundAppRespVO::new)
                .toList();
        
        // 提取问题内容列表
        List<String> questionContentList = roundList.stream()
                .map(round -> round.getQuestionList().stream()
                        .map(WordMatchingBaseInfoAppRespVO::getQuestionContent)
                        .filter(CharSequenceUtil::isNotBlank)
                        .collect(Collectors.joining("，"))
                )
                .toList();

        // 设置收藏状态
        setWordMatchingFavoriteStatus(roundList, StpUtil.getLoginIdAsLong());
        
        // 设置响应数据
        vo.setRoundList(roundList);
        vo.setQuestionContentList(questionContentList);
        vo.setTotalRound(totalRound);
        vo.setCompletedRound(Math.min(completedRound, totalRound));
        
        return vo;
    }

    /**
     * 处理其他类型的练习
     */
    private SpecialExerciseAppRespVO handleOtherExerciseTypes(SpecialExerciseAppRespVO vo, 
                                                         List<ExerciseQuestionAppRespVO> appRespVoList,
                                                         List<Long> practicedQuestionVersionIds,
                                                         List<ExerciseQuestionRespVO> respVOList,
                                                         Integer type) {
        // 按排序和ID排序
        appRespVoList = appRespVoList.stream().sorted(Comparator
                        .comparing(ExerciseQuestionAppRespVO::getSort, Comparator.nullsLast(Integer::compareTo))
                        .thenComparing(ExerciseQuestionAppRespVO::getId, Comparator.nullsLast(Long::compareTo)))
                .toList();
        
        // 处理连词成句类型
        if (SpecialExerciseTypeEnum.WORD_TO_SENTENCE.getCode().equals(type)) {
            List<String> knowledgePointList = appRespVoList.stream()
                    .map(ExerciseQuestionAppRespVO::getKnowledgePoint)
                    .filter(CharSequenceUtil::isNotBlank)
                    .toList();
            vo.setKnowledgePointList(knowledgePointList);
        }
        
        // 计算已完成的轮次
        int completedRound = (int) respVOList.stream()
                .map(ExerciseQuestionRespVO::getExerciseQuestionVersionId)
                .filter(practicedQuestionVersionIds::contains)
                .filter(Objects::nonNull)
                .distinct()
                .count();
        
        // 提取问题内容列表
        List<String> questionContentList = appRespVoList.stream()
                .map(ExerciseQuestionAppRespVO::getQuestionContent)
                .filter(CharSequenceUtil::isNotBlank)
                .toList();
        
        // 设置响应数据
        vo.setQuestionContentList(questionContentList);
        vo.setExerciseQuestionList(appRespVoList);
        vo.setTotalRound(appRespVoList.size());
        
        int maxRound = appRespVoList.size();
        
        vo.setCompletedRound(Math.min(completedRound, maxRound));
        
        return vo;
    }

    /**
     * 为题目列表设置用户收藏状态
     *
     * @param questionList 题目响应对象列表
     * @param userId       用户ID
     * @param type         题目类型
     */
    private void setFavoriteStatus(List<ExerciseQuestionAppRespVO> questionList, Long userId, Integer type) {
        if (CollUtil.isEmpty(questionList)) {
            return;
        }

        List<Long> questionIds = questionList.stream()
                .map(ExerciseQuestionAppRespVO::getQuestionId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        if (CollUtil.isEmpty(questionIds)) {
            return;
        }

        Integer favoriteType = isMiniGameType(type)
                ? FavoriteTypeEnum.VOCABULARY.getCode()
                : FavoriteTypeEnum.MINI_GAME_QUESTION.getCode();

        Map<Long, Boolean> favoriteStatusMap = userFavoriteApi.selectStatus(userId, questionIds, favoriteType);

        questionList.forEach(item ->
                item.setCollect(favoriteStatusMap.getOrDefault(item.getQuestionId(), false))
        );
    }

    /**
     * 为单词连连看题目列表设置用户收藏状态
     *
     * @param roundList 题目响应对象列表
     * @param userId    用户ID
     */
    private void setWordMatchingFavoriteStatus(List<ExerciseRoundAppRespVO> roundList, Long userId) {
        if (CollUtil.isEmpty(roundList) || userId == null) {
            return;
        }

        // 收集所有的题目id
        List<Long> allQuestionIds = roundList.stream()
                .flatMap(round -> Optional.ofNullable(round.getQuestionList())
                        .orElse(Collections.emptyList())
                        .stream())
                .map(WordMatchingBaseInfoAppRespVO::getQuestionId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();


        if (CollUtil.isEmpty(allQuestionIds)) {
            return;
        }

        // 一次性查询所有收藏状态
        Map<Long, Boolean> favoriteStatusMap = userFavoriteApi.selectStatus(userId, allQuestionIds, FavoriteTypeEnum.VOCABULARY.getCode());

        // 遍历每一轮，判断是否全部收藏
        for (ExerciseRoundAppRespVO appRespVO : roundList) {
            List<WordMatchingBaseInfoAppRespVO> questionList = appRespVO.getQuestionList();
            if (CollUtil.isEmpty(questionList)) {
                appRespVO.setCollect(false);
                continue;
            }

            boolean allFavorited = questionList.stream()
                    .map(WordMatchingBaseInfoAppRespVO::getQuestionId)
                    .filter(Objects::nonNull)
                    .allMatch(id -> Boolean.TRUE.equals(favoriteStatusMap.get(id)));

            appRespVO.setCollect(allFavorited);
        }
    }


    /**
     * 判断当前题目类型
     *
     * @param type 题目类型
     * @return 返回 true；否则返回 false
     */
    private boolean isMiniGameType(Integer type) {
        return SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(type)
                || SpecialExerciseTypeEnum.STROKE_WRITING.getCode().equals(type);
    }



    /**
     * 获取专项练习练习题数
     */
    public SpecialExerciseCountAppRespVO getSpecialExercisePracticeCount(SpecialExerciseAppReqVO reqVO) {

        SpecialExerciseCountAppRespVO vo = new SpecialExerciseCountAppRespVO();

        Integer type = reqVO.getType();
        SpecialExerciseTypeEnum specialExerciseTypeEnum = SpecialExerciseTypeEnum.getByCode(type);
        if (specialExerciseTypeEnum == null || reqVO.getHskLevel() == null) {
            return vo;
        }

        List<SpecialExerciseDO> specialExerciseList = specialExerciseService.lambdaQuery()
                .eq(SpecialExerciseDO::getIsShow, IsShowEnum.SHOW.getCode())
                .eq(SpecialExerciseDO::getType, type)
                .eq(SpecialExerciseDO::getHskLevel, reqVO.getHskLevel())
                .list();

        List<Long> specialExerciseIdList = specialExerciseList.stream()
                .map(SpecialExerciseDO::getId)
                .toList();

        if (CollUtil.isEmpty(specialExerciseIdList)) {
            return vo;
        }

        int completedNumber = 0;

        // 如果用户已经登录，获取用户练习的题目数量
        if (StpUtil.isLogin()) {
            long userId = StpUtil.getLoginIdAsLong();

            long count = gameRecordSummaryService.lambdaQuery()
                    .eq(GameRecordSummaryDO::getUserId, userId)
                    .in(GameRecordSummaryDO::getSpecialExerciseId, specialExerciseIdList)
                    .eq(GameRecordSummaryDO::getPracticeStatus, GameRecordPracticeStatusEnum.COMPLETED.getCode())
                    .list()
                    .stream()
                    .map(GameRecordSummaryDO::getSpecialExerciseId)
                    .distinct()
                    .filter(Objects::nonNull)
                    .count();

            completedNumber = Math.toIntExact(count);
        }

        vo.setTotalNumber(specialExerciseList.size());
        vo.setCompletedNumber(completedNumber);
        vo.setSpecialPracticeType(type);

        return vo;
    }

    /**
     * 开始练习
     */
    @Transactional(rollbackFor = Exception.class)
    public SpecialExerciseStartAppRespVO startSpecialExercise(SpecialExerciseAppReqVO reqVO) {
        // 验证专项练习是否存在
        SpecialExerciseDO specialExercise = validateAndGetSpecialExercise(reqVO.getSpecialExerciseId());

        Long userId = StpUtil.getLoginIdAsLong();

        // 验证用户剩余批改次数
        gameCorrectionAppManager.validateCorrectionCount(specialExercise.getType());

        // 如果是继续练习模式，处理继续练习逻辑
        if (SpecialExerciseAnswerModeEnum.CONTINUE.getCode().equals(reqVO.getAnswerType())) {
            return handleContinueMode(reqVO.getRecordSummaryId(), userId, reqVO.getSource());
        }

        // 创建新的练习记录
        SpecialExerciseStartAppRespVO gameRecord = createNewGameRecord(specialExercise,
            reqVO.getType(), userId, reqVO.getSource(), reqVO.getInteractiveCourseUnitId());

        // ==================================发送互动课练习事件=======================================
        if (reqVO.getInteractiveCourseUnitId() != null
            && SpecialExerciseRecordSourceEnum.INTERACTIVE_COURSE.getCode().equals(reqVO.getSource())) {
            // 只有当来源是互动课时才发送事件
            gameProducer.interactiveCourseMessage(userId, reqVO.getInteractiveCourseUnitId(),
                specialExercise.getId(),
                gameRecord.getRecordSummaryId());
        }
        // ==================================发送互动课练习事件=======================================
        return gameRecord;
    }

    /**
     * 验证并获取专项练习
     */
    private SpecialExerciseDO validateAndGetSpecialExercise(Long specialExerciseId) {
        SpecialExerciseDO specialExercise = specialExerciseService.lambdaQuery()
                .eq(SpecialExerciseDO::getId, specialExerciseId)
                .eq(SpecialExerciseDO::getIsShow, IsShowEnum.SHOW.getCode())
                .last("limit 1")
                .one();

        if (specialExercise == null) {
            throw exception(SPECIAL_EXERCISE_NOT_EXISTS);
        }

        return specialExercise;
    }

    /**
     * 处理继续练习模式
     */
    private SpecialExerciseStartAppRespVO handleContinueMode(Long recordSummaryId, Long userId, Integer source) {
        GameRecordSummaryDO recordSummary = gameRecordSummaryService.getById(recordSummaryId);

        if (recordSummary == null
                || !Objects.equals(recordSummary.getUserId(), userId)
                || !Objects.equals(recordSummary.getSource(), source)) {
            throw exception(GAME_RECORD_NOT_EXISTS);
        }

        // 检查练习记录是否已完成
        if (isRecordCompleted(recordSummary)) {
            throw exception(GAME_RECORD_COMPLETED);
        }
        return new SpecialExerciseStartAppRespVO(recordSummary.getId());
    }

    /**
     * 检查练习记录是否已完成
     */
    private boolean isRecordCompleted(GameRecordSummaryDO recordSummary) {
        return Objects.equals(GameRecordPracticeStatusEnum.COMPLETED.getCode(), recordSummary.getPracticeStatus());
    }

    /**
     * 创建新的游戏记录
     */
    private SpecialExerciseStartAppRespVO createNewGameRecord(SpecialExerciseDO specialExercise, Integer type, Long userId,
        Integer source, Long interactiveCourseUnitId) {
        // 获取题目数量
        List<ExerciseQuestionVersionDO> practiceQuestionsList = getPracticeQuestionsList(specialExercise);
        int totalQuestions = practiceQuestionsList.size();

        List<Long> exerciseQuestionVersionIds = practiceQuestionsList.stream()
                .map(ExerciseQuestionVersionDO::getId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        gameRecordSummaryService.lambdaUpdate()
            .eq(GameRecordSummaryDO::getUserId, userId)
            .eq(GameRecordSummaryDO::getSpecialExerciseId, specialExercise.getId())
            .eq(GameRecordSummaryDO::getType, type)
            .set(GameRecordSummaryDO::getIsNewest, 0)
            .eq(GameRecordSummaryDO::getSource, source)
            .update();

        // 创建新的练习记录
        GameRecordSummaryDO recordSummary = buildGameRecordSummary(specialExercise, userId, totalQuestions, source, exerciseQuestionVersionIds, interactiveCourseUnitId);
        gameRecordSummaryService.save(recordSummary);

        return new SpecialExerciseStartAppRespVO(recordSummary.getId());
    }

    /**
     * 获取题目总数
     */
    private int getTotalQuestionsCount(SpecialExerciseDO specialExercise) {
        boolean isWordMatching = SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(specialExercise.getType());

        Long count = exerciseQuestionVersionService.lambdaQuery()
                .eq(ExerciseQuestionVersionDO::getSpecialExerciseId, specialExercise.getId())
                .eq(ExerciseQuestionVersionDO::getVersion, specialExercise.getRelatedVersion())
                .eq(!isWordMatching, ExerciseQuestionVersionDO::getIsShow, IsShowEnum.SHOW.getCode())
                .eq(isWordMatching, ExerciseQuestionVersionDO::getShowRound, IsShowEnum.SHOW.getCode())
                .count();

        return Math.toIntExact(count);
    }

    /**
     * 获取题目列表
     */
    private List<ExerciseQuestionVersionDO> getPracticeQuestionsList(SpecialExerciseDO specialExercise) {
        boolean isWordMatching = SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(specialExercise.getType());

        return exerciseQuestionVersionService.lambdaQuery()
                .eq(ExerciseQuestionVersionDO::getSpecialExerciseId, specialExercise.getId())
                .eq(ExerciseQuestionVersionDO::getVersion, specialExercise.getRelatedVersion())
                .eq(!isWordMatching, ExerciseQuestionVersionDO::getIsShow, IsShowEnum.SHOW.getCode())
                .eq(isWordMatching, ExerciseQuestionVersionDO::getShowRound, IsShowEnum.SHOW.getCode())
                .list();
    }

    /**
     * 构建游戏记录汇总对象
     */
    private GameRecordSummaryDO buildGameRecordSummary(SpecialExerciseDO specialExercise, Long userId, int totalQuestions,
                                                       Integer source, List<Long> exerciseQuestionVersionIds, Long interactiveCourseUnitId) {
        GameRecordSummaryDO recordSummary = new GameRecordSummaryDO();
        recordSummary.setUserId(userId);
        recordSummary.setSpecialExerciseId(specialExercise.getId());
        recordSummary.setType(specialExercise.getType());
        recordSummary.setTotalQuestions(totalQuestions);
        recordSummary.setAnsweredQuestions(0);
        recordSummary.setCorrectQuestions(0);
        recordSummary.setWrongQuestions(0);
        recordSummary.setUnansweredQuestions(0);
        recordSummary.setProgress(BigDecimal.ZERO);
        recordSummary.setPracticeStatus(GameRecordPracticeStatusEnum.IN_PROGRESS.getCode());
        recordSummary.setIsNewest(1);
        recordSummary.setSource(source);
        recordSummary.setAnswerStartTime(LocalDateTime.now());
        recordSummary.setAnswerDate(LocalDate.now());
        recordSummary.setQuestionVersion(specialExercise.getRelatedVersion());
        recordSummary.setExerciseQuestionVersionIds(JSONUtil.toJsonStr(exerciseQuestionVersionIds));
        // 设置互动课单元ID（如果来源于互动课）
        recordSummary.setInteractiveCourseUnitId(interactiveCourseUnitId);

        return recordSummary;
    }

    /**
     * 获取专项练习报告
     */
    public GameReportAppRespVO report(SpecialExerciseAppReqVO reqVO) {
        long userId = StpUtil.getLoginIdAsLong();
        Long specialExerciseId = reqVO.getSpecialExerciseId();
        Long recordSummaryId = reqVO.getRecordSummaryId();

        // 验证专项练习是否存在
        SpecialExerciseDO specialExercise = validateSpecialExercise(specialExerciseId);

        // 获取游戏记录汇总
        GameRecordSummaryDO recordSummary = getCompletedGameRecord(userId, recordSummaryId);

        // 获取练习题目
        List<Long> versionIdList = JSONUtil.toList(recordSummary.getExerciseQuestionVersionIds(), Long.class);

        // 获取记录详情
        List<GameRecordDetailsDO> recordDetailsList = getGameRecordDetails(specialExerciseId, recordSummary.getId(), recordSummary.getType(), userId);

        // 构建报告
        return buildGameReport(specialExercise, recordSummary, versionIdList, recordDetailsList);
    }

    /**
     * 验证专项练习是否存在
     */
    private SpecialExerciseDO validateSpecialExercise(Long specialExerciseId) {
        SpecialExerciseDO specialExercise = specialExerciseService.getById(specialExerciseId);
        if (specialExercise == null) {
            throw exception(SPECIAL_EXERCISE_NOT_EXISTS);
        }
        return specialExercise;
    }

    /**
     * 获取已完成的游戏记录
     */
    private GameRecordSummaryDO getCompletedGameRecord(Long userId, Long recordSummaryId) {
        GameRecordSummaryDO recordSummary = gameRecordSummaryService.getById(recordSummaryId);

        if (recordSummary == null
                || !Objects.equals(recordSummary.getUserId(), userId)
                || !GameRecordPracticeStatusEnum.COMPLETED.getCode().equals(recordSummary.getPracticeStatus())) {
            throw exception(GAME_RECORD_NOT_EXISTS);
        }
        return recordSummary;
    }

    /**
     * 获取练习题目
     */
    private List<ExerciseQuestionDO> getExerciseQuestions(Long specialExerciseId) {
        List<ExerciseQuestionDO> exerciseQuestionList = exerciseQuestionService.lambdaQuery()
                .eq(ExerciseQuestionDO::getSpecialExerciseId, specialExerciseId)
                .orderByAsc(ExerciseQuestionDO::getShowRound)
                .list();

        if (CollUtil.isEmpty(exerciseQuestionList)) {
            throw exception(GAME_RECORD_NOT_EXISTS);
        }
        return exerciseQuestionList;
    }

    /**
     * 获取游戏记录详情
     */
    private List<GameRecordDetailsDO> getGameRecordDetails(Long specialExerciseId, Long recordSummaryId, Integer type, Long userId) {
        return gameRecordDetailsService.lambdaQuery()
                .eq(GameRecordDetailsDO::getSpecialExerciseId, specialExerciseId)
                .eq(GameRecordDetailsDO::getRecordSummaryId, recordSummaryId)
                .eq(GameRecordDetailsDO::getType, type)
                .eq(GameRecordDetailsDO::getUserId, userId)
                .list();
    }

    /**
     * 构建游戏报告
     */
    private GameReportAppRespVO buildGameReport(SpecialExerciseDO specialExercise,
                                                GameRecordSummaryDO recordSummary,
                                                List<Long> versionIdList,
                                                List<GameRecordDetailsDO> recordDetailsList) {
        // 初始化基础报告信息
        GameReportAppRespVO vo = new GameReportAppRespVO();
        vo.setSpecialExerciseId(specialExercise.getId());
        vo.setType(recordSummary.getType());
        vo.setName(specialExercise.getNameCn());
        vo.setAccuracy(getScore(recordSummary.getAccuracy(), recordSummary.getId(), recordSummary.getType(), recordSummary.getTotalQuestions()));
        vo.setAverageScore(getScore(recordSummary.getAverageScore(), recordSummary.getId(), recordSummary.getType(), recordSummary.getTotalQuestions()));

        if(CollUtil.isEmpty(recordDetailsList)){
            return vo;
        }

        // 创建版本ID到顺序的映射，用于排序
        Map<Long, Integer> orderMap = new HashMap<>(versionIdList.size());
        for (int i = 0; i < versionIdList.size(); i++) {
            orderMap.put(versionIdList.get(i), i);
        }

        // 根据练习类型获取记录详情映射
        Map<Long, GameRecordDetailsDO> recordDetailsMap = getRecordDetailsMapByType(recordSummary.getType(), recordDetailsList);

        // 转换为响应VO并排序
        List<GameReportDetailsAppRespVO> appRespList = recordDetailsMap.values().stream()
                .map(this::createDetailedReportDetails)
                .sorted(Comparator.comparingInt(
                        item -> orderMap.getOrDefault(item.getExerciseQuestionVersionId(), Integer.MAX_VALUE)
                ))
                .toList();

        // 设置收藏状态
        setFavoriteStatusForReport(appRespList, StpUtil.getLoginIdAsLong(), recordSummary.getType());

        // 根据练习类型处理报告
        if (SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(recordSummary.getType())) {
            return buildWordMatchingReport(vo, appRespList);
        }

        // 其他类型练习直接设置正确列表
        vo.setCorrectList(appRespList);
        return vo;
    }

    /**
     * 设置报告中题目的收藏状态
     *
     * @param voList 题目列表
     * @param userId 用户ID
     * @param type   类型
     */
    private void setFavoriteStatusForReport(List<GameReportDetailsAppRespVO> voList, Long userId, Integer type) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }
        List<Long> questionIds = voList.stream()
                .map(GameReportDetailsAppRespVO::getQuestionId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        if (CollUtil.isEmpty(questionIds)) {
            return;
        }

        Integer favoriteType = isMiniGameType(type)
                ? FavoriteTypeEnum.VOCABULARY.getCode()
                : FavoriteTypeEnum.MINI_GAME_QUESTION.getCode();

        Map<Long, Boolean> favoriteStatusMap = userFavoriteApi.selectStatus(userId, questionIds, favoriteType);

        voList.forEach(item ->
                item.setCollect(favoriteStatusMap.getOrDefault(item.getQuestionId(), false))
        );

    }

    /**
     * 获取分数
     *
     * @param score           得分
     * @param recordSummaryId 总记录ID
     * @param type            类型
     * @param totalQuestions  总题数
     * @return int 分数
     */
    private int getScore(BigDecimal score, Long recordSummaryId, Integer type, Integer totalQuestions) {
        if (score != null) {
            return score.intValue();
        }
        if (totalQuestions == null || totalQuestions <= 0) {
            return 0;
        }

        // 获取答题详情记录
        List<GameRecordDetailsDO> recordDetailsList = gameRecordDetailsService.lambdaQuery()
                .eq(GameRecordDetailsDO::getRecordSummaryId, recordSummaryId)
                .list();

        if (CollUtil.isEmpty(recordDetailsList)) {
            return 0;
        }

        // 根据练习类型选择不同的处理方式
        Map<Long, GameRecordDetailsDO> recordDetailsMap = getRecordDetailsMapByType(type, recordDetailsList);

        // 计算基础统计数据
        int correctQuestions = 0;

        // 根据练习类型执行不同的统计逻辑
        if (SpecialExerciseTypeEnum.KARAOKE.getCode().equals(type)) {
            // 计算总分和已答题数量
            int totalScore = recordDetailsMap.values().stream()
                    .filter(e -> Objects.nonNull(e.getScore()))
                    .mapToInt(GameRecordDetailsDO::getScore)
                    .sum();

            // 计算平均分
            BigDecimal averageScore = BigDecimal.valueOf(totalScore)
                    .divide(BigDecimal.valueOf(totalQuestions), 4, RoundingMode.HALF_UP);

            return averageScore.intValue();
        } else {

            for (GameRecordDetailsDO recordDetails : recordDetailsMap.values()) {
                if (Objects.equals(recordDetails.getAnswerStatus(), 1)) {
                    if (GameRecordCorrectTypeEnum.CORRECT.getCode().equals(recordDetails.getIsCorrect())) {
                        correctQuestions++;
                    }
                }
            }

            // 计算正确率
            BigDecimal accuracy = BigDecimal.valueOf(correctQuestions)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(totalQuestions), 4, RoundingMode.HALF_UP);

            return accuracy.intValue();
        }
    }

    /**
     * 根据练习类型获取记录详情映射
     */
    private Map<Long, GameRecordDetailsDO> getRecordDetailsMapByType(Integer type, List<GameRecordDetailsDO> recordDetailsList) {
        if (SpecialExerciseTypeEnum.STROKE_WRITING.getCode().equals(type)) {
            // 笔画书写：按题目ID分组
            return recordDetailsList.stream()
                    .collect(Collectors.toMap(
                            GameRecordDetailsDO::getExerciseQuestionId,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
        } else if (SpecialExerciseTypeEnum.WORD_MATCHING.getCode().equals(type)) {
            // 单词连连看：只取第一次答题的记录
            return recordDetailsList.stream()
                    .filter(e -> Objects.equals(e.getAnswerSequence(), 1))
                    .collect(Collectors.toMap(
                            GameRecordDetailsDO::getExerciseQuestionId,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));
        } else {
            // 其他类型：取最后一次答题的记录
            return recordDetailsList.stream()
                    .collect(Collectors.toMap(
                            GameRecordDetailsDO::getExerciseQuestionId,
                            Function.identity(),
                            (e1, e2) -> e1.getAnswerTime().isAfter(e2.getAnswerTime()) ? e1 : e2
                    ));
        }
    }

    private GameReportAppRespVO buildWordMatchingReport(GameReportAppRespVO vo, List<GameReportDetailsAppRespVO> appRespList) {
        List<GameReportDetailsAppRespVO> correctList = new ArrayList<>();
        List<GameReportDetailsAppRespVO> wrongList = new ArrayList<>();

        for (GameReportDetailsAppRespVO respVO : appRespList) {
            if (GameRecordCorrectTypeEnum.CORRECT.getCode().equals(respVO.getIsCorrect())) {
                correctList.add(respVO);
            } else if (GameRecordCorrectTypeEnum.ERROR.getCode().equals(respVO.getIsCorrect())) {
                wrongList.add(respVO);
            }
        }
        vo.setWrongList(wrongList);
        vo.setCorrectList(correctList);

        return vo;
    }

    /**
     * 创建基础报告详情
     */
    private GameReportDetailsAppRespVO createReportDetails(GameRecordDetailsDO recordDetails) {
        GameReportDetailsAppRespVO reportDetails = new GameReportDetailsAppRespVO();
        reportDetails.setQuestionContent(recordDetails.getQuestionContent());
        reportDetails.setTranslationOt(recordDetails.getReferenceAnswer());
        return reportDetails;
    }

    /**
     * 创建详细报告详情
     */
    private GameReportDetailsAppRespVO createDetailedReportDetails(GameRecordDetailsDO recordDetails) {
        GameReportDetailsAppRespVO reportDetails = createReportDetails(recordDetails);
        reportDetails.setIsCorrect(recordDetails.getIsCorrect());
        reportDetails.setReferenceAnswer(recordDetails.getReferenceAnswer());
        reportDetails.setUserAnswer(recordDetails.getUserAnswer());
        reportDetails.setScore(recordDetails.getScore());
        reportDetails.setPinyin(recordDetails.getPinyin());
        reportDetails.setExerciseQuestionVersionId(recordDetails.getExerciseQuestionVersionId());
        reportDetails.setAnswerStatus(recordDetails.getAnswerStatus());
        reportDetails.setQuestionId(recordDetails.getQuestionId());
        return reportDetails;
    }

    /**
     * 设置报告统计信息
     */
    private GameReportAppRespVO setReportStatistics(GameReportAppRespVO vo,
                                                    List<GameReportDetailsAppRespVO> correctList,
                                                    List<GameReportDetailsAppRespVO> wrongList,
                                                    int totalQuestions) {
        vo.setCorrectList(correctList);
        vo.setWrongList(wrongList);
        vo.setTotalQuestions(totalQuestions);
        vo.setCorrectQuestions(correctList.size());
        vo.setWrongQuestions(wrongList.size());
        vo.setAccuracy(calculateAccuracy(correctList.size(), totalQuestions));

        return vo;
    }

    /**
     * 计算正确率
     */
    private int calculateAccuracy(int correctCount, int totalCount) {
        return totalCount == 0 ? 0 : Math.round((float) correctCount / totalCount * 100);
    }
}