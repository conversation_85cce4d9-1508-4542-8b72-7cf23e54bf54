package com.xt.hsk.framework.common.enums;

import lombok.Getter;

import java.util.List;

@Getter
public enum PracticeModeEnum implements BasicEnum<Integer> {
    //练习模式：1-题型练习 2-全真模考 3-30分钟模考 4-15分钟模考 5互动课 6 收藏夹 7 等级自测
    TYPE_PRACTICE(1, "题型练习"),
    EXAM(2, "全真模考"),
    THIRTY_MINUTE_EXAM(3, "30分钟模考"),
    FIFTEEN_MINUTE_EXAM(4, "15分钟模考"),
    INTERACTIVE_COURSE(5, "互动课"),
    FAVORITE(6, "收藏夹"),
    LEVEL_TEST(7, "等级自测");
    private final Integer code;
    private final String desc;

    PracticeModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取模考类型code
     *
     * @return List<Integer> 模考类型code列表
     */
    public static List<Integer> getExamCodes() {
        return List.of(EXAM.getCode(), THIRTY_MINUTE_EXAM.getCode(), FIFTEEN_MINUTE_EXAM.getCode());
    }

    /**
     * 获取模考类型枚举
     *
     * @return List<PracticeModeEnum> 模考类型枚举列表
     */
    public static List<PracticeModeEnum> getExamEnums() {
        return List.of(EXAM, THIRTY_MINUTE_EXAM, FIFTEEN_MINUTE_EXAM);
    }
}
